# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- **Build**: `./gradlew build` - Compiles and tests the project
- **Test**: `./gradlew test` - Runs all tests with JUnit Platform
- **Single Test**: `./gradlew test --tests "ClassName.methodName"` - Run specific test method
- **Clean**: `./gradlew clean` - Removes build artifacts 
- **Run**: `./gradlew bootRun` - Starts Spring Boot application on port 8080
- **Clean Build**: `./gradlew clean build` - Full clean and rebuild

## Architecture Overview

This is a Spring Boot 3.5.0 platform service built with Java 21 that provides:

- **Auth0 Integration**: OAuth2 resource server with opaque token introspection
- **Multi-tenancy**: Organization-based resource isolation with user-organization bindings
- **Billing System**: LLM and search usage tracking with daily billing aggregation
- **Payment Processing**: Stripe integration for organization wallet management
- **Microservice Ready**: RESTful APIs with standardized response format

### Core Domain Structure

The application follows a layered architecture organized by business domains:

```
com.myhispreadnlp.platform/
├── common/           # Shared utilities, constants, security config
├── jpa/             # Data layer with repositories and entities
│   ├── organization/ # Org info, secrets, billing, wallet entities
│   ├── system/      # LLM/search models, pricing, rates
│   └── user/        # User info and org bindings
├── organization/    # Organization business logic
└── user/           # User management services
```

### Key Architectural Patterns

- **Repository Pattern**: JPA repositories for data access
- **Service Layer**: Business logic separated from controllers
- **DTO Pattern**: Request/Response objects in `common/request|response`
- **Constructor Injection**: Uses `@RequiredArgsConstructor` with `private final`
- **Thread-Local Context**: `UserContextHolder` for current user access
- **Standardized Responses**: `Result<T>` wrapper with `ErrorCodeEnum` codes

### Authentication & Security

- Auth0 OAuth2 with custom `Auth0UserInfoIntrospector`
- User context automatically injected via `CurrentUser` utility
- All `/api/*` endpoints require authentication except `/api/public`
- CSRF disabled, CORS enabled for API usage

### Database & Caching

- **PostgreSQL**: Primary database with Hibernate JPA
- **Redis**: Caching and session storage with Lettuce client
- **Connection Pooling**: HikariCP with optimized settings
- **Naming Strategy**: CamelCase to snake_case conversion

## Code Style Guidelines

From existing AGENT.md patterns:

- **Packages**: Follow `com.myhispreadnlp.platform.domain.feature` structure
- **Classes**: PascalCase (e.g., `UserContextHolder`, `ErrorCodeEnum`)
- **Methods/Variables**: camelCase (e.g., `getUserContext`, `requestId`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `SYSTEM_ERROR`)
- **Dependency Injection**: Use `@RequiredArgsConstructor` with `private final` fields
- **Error Handling**: Use `ErrorCodeEnum` for consistent error codes and messages

## Important Configuration

- **Port**: 8080 (configured in application.properties)
- **Database**: PostgreSQL with batch operations optimized
- **Virtual Threads**: Enabled for better concurrency
- **Graceful Shutdown**: 60-second timeout configured
- **Time Zone**: UTC for all timestamps