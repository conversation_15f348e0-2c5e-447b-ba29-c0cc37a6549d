package com.myhispreadnlp.platform;

import com.myhispreadnlp.platform.common.utils.CurrentUser;
import com.myhispreadnlp.platform.jpa.user.info.bean.UserInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController @RequestMapping("/api")
@RequiredArgsConstructor
public class ApiController {

    private final CurrentUser current;

    @GetMapping("/me")
    public Map<String,Object> me() {
        if (!current.available())
            throw new IllegalStateException("用户未初始化");

        UserInfo u = current.get();
        return Map.of(
                "id", u.getId(),
                "email", u.getEmail(),
                "name", u.getName()
        );
    }
}

