package com.myhispread.myhispreaddealer.common.exception;


import com.myhispread.myhispreaddealer.common.constant.ErrorCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CognitionException extends RuntimeException {

    private ErrorCodeEnum errorCodeEnum;

    public CognitionException(ErrorCodeEnum errorCodeEnum) {
        super(errorCodeEnum.getDesc());
        this.errorCodeEnum = errorCodeEnum;
    }


}
