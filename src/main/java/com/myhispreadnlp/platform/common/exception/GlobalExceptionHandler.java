package com.myhispread.myhispreaddealer.common.exception;


import com.myhispread.myhispreaddealer.common.constant.ErrorCodeEnum;
import com.myhispread.myhispreaddealer.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Result<Void>> handleException(Exception e) {
        log.error("Internal Server Error", e);
        return ResponseEntity.status(500)
                             .contentType(MediaType.APPLICATION_JSON)
                             .body(Result.failedResult(ErrorCodeEnum.SYSTEM_ERROR));
    }


    @ExceptionHandler(CognitionException.class)
    public ResponseEntity<Result<Void>> handleCognitionException(CognitionException e) {
        return ResponseEntity.status(200)
                             .contentType(MediaType.APPLICATION_JSON)
                             .body(Result.failedResult(e.getErrorCodeEnum()));
    }

}
