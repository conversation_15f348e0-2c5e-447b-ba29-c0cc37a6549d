package com.myhispreadnlp.platform.common.system.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.myhispreadnlp.platform.common.utils.CurrentUser;
import com.myhispreadnlp.platform.jpa.user.info.bean.UserInfo;
import com.myhispreadnlp.platform.user.info.service.UserInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.*;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.core.DefaultOAuth2AuthenticatedPrincipal;
import org.springframework.security.oauth2.core.OAuth2AuthenticatedPrincipal;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.oidc.StandardClaimNames;
import org.springframework.security.oauth2.server.resource.introspection.OpaqueTokenIntrospector;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class Auth0UserInfoIntrospector implements OpaqueTokenIntrospector {

    private final String userInfoUrl;
    private final RestTemplate rest = new RestTemplate();
    private final ObjectMapper mapper = new ObjectMapper();

    private final CurrentUser currentUser;
    private final UserInfoService userInfoService;

    public Auth0UserInfoIntrospector(
            @org.springframework.beans.factory.annotation.Value("${auth0.userinfo-endpoint}") String userInfoUrl, CurrentUser currentUser, UserInfoService userInfoService) {
        this.userInfoUrl = userInfoUrl;
        this.currentUser = currentUser;
        this.userInfoService = userInfoService;
    }

    /** 15 分钟缓存 */
    @Override
    @Cacheable(value = "userinfo", key = "#token", sync = true)
    public OAuth2AuthenticatedPrincipal introspect(String token) {

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));

        ResponseEntity<String> rsp;
        try {
            rsp = rest.exchange(
                    userInfoUrl,
                    HttpMethod.GET,
                    new HttpEntity<>(headers),
                    String.class);
        } catch (RestClientException ex) {
            throw new OAuth2AuthenticationException(new OAuth2Error("invalid_token",
                    "UserInfo request failed", null), ex.getMessage());
        }


        if (!rsp.getStatusCode().is2xxSuccessful()) {
            throw new OAuth2AuthenticationException(new OAuth2Error("invalid_token",
                    "Token not active", null));
        }

        Map<String, Object> claims;
        try {
            claims = mapper.readValue(rsp.getBody(),
                    new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            throw new OAuth2AuthenticationException(new OAuth2Error("invalid_response",
                    "Cannot parse /userinfo response", null), e.getMessage());
        }

        UserInfo user = userInfoService.ensureUser(claims); // 幂等
        currentUser.set(user);                      // ② 放进 RequestScope

        /* 权限示例：固定 ROLE_USER，可按业务自定义 */
        Collection<GrantedAuthority> authorities =
                List.of(new SimpleGrantedAuthority("ROLE_USER"));

        String principalName =
                Optional.ofNullable(claims.get(StandardClaimNames.SUB))
                        .map(Object::toString)
                        .orElse("unknown");

        return new DefaultOAuth2AuthenticatedPrincipal(
                principalName, claims, authorities);
    }
}