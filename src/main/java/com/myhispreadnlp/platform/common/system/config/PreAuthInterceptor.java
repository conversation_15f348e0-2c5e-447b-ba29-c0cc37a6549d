package com.myhispreadnlp.platform.common.system.config;

import com.myhispreadnlp.platform.common.constant.SystemConstant;
import com.myhispreadnlp.platform.common.utils.CurrentUser;
import com.myhispreadnlp.platform.jpa.user.bind.bean.UserOrganizationBind;
import com.myhispreadnlp.platform.jpa.user.bind.repository.UserOrganizationBindRepository;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.core.OAuth2AuthenticatedPrincipal;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.server.resource.introspection.OpaqueTokenIntrospector;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Slf4j
@Component
public class PreAuthInterceptor implements OpaqueTokenIntrospector {

    private final OpaqueTokenIntrospector delegate;

    private final UserOrganizationBindRepository userOrganizationBindRepository;

    private final CurrentUser currentUser;

    public PreAuthInterceptor(Auth0UserInfoIntrospector auth0UserInfoIntrospector, UserOrganizationBindRepository userOrganizationBindRepository, CurrentUser currentUser) {
        this.delegate = auth0UserInfoIntrospector;
        this.userOrganizationBindRepository = userOrganizationBindRepository;
        this.currentUser = currentUser;
    }

    @Override
    public OAuth2AuthenticatedPrincipal introspect(String token) {
        log.debug("PreAuthInterceptor: Delegating to Auth0UserInfoIntrospector first");
        
        // Delegate to the actual Auth0 introspector first
        OAuth2AuthenticatedPrincipal principal = delegate.introspect(token);
        
        log.debug("PreAuthInterceptor: Processing post-Auth0 introspection logic");
        
        // Post-processing logic after Auth0 introspection
        if (getRequest().getParameterValues("organizationId") != null) {
            UserOrganizationBind userCharactersBind = userOrganizationBindRepository.findUserOrganizationBindByUserIdAndOrganizationIdAndStatuses( currentUser.get().getId(),Long.valueOf(getRequest().getParameterValues("organizationId")[0]), SystemConstant.AVAILABLE);
            if (userCharactersBind != null) {
                currentUser.setPermission(userCharactersBind.getPermission());
            }
        }

        return principal;
    }


    public static HttpServletRequest getRequest() {
        ServletRequestAttributes attributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new IllegalStateException("No request context available");
        }
        return attributes.getRequest();
    }
}