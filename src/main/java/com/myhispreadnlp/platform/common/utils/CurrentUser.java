package com.myhispreadnlp.platform.common.utils;

import com.myhispreadnlp.platform.jpa.user.info.bean.UserInfo;
import org.springframework.stereotype.Component;
import org.springframework.web.context.annotation.RequestScope;

@Component
@RequestScope
public class CurrentUser {

    private UserInfo userInfo;

    private Integer permission;


    public UserInfo get() { return userInfo; }

    public void set(UserInfo u) { this.userInfo = u; }

    public Integer getPermission() { return permission; }

    public void setPermission(Integer p) { this.permission = p; }

    public boolean available() { return userInfo != null; }
}