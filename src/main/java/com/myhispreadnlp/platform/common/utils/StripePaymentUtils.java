package com.myhispreadnlp.platform.common.utils;

import com.stripe.exception.CardException;
import com.stripe.exception.StripeException;
import com.stripe.model.Charge;
import com.stripe.model.PaymentIntent;
import com.stripe.model.Refund;
import com.stripe.model.billingportal.Configuration;
import com.stripe.model.billingportal.Session;
import com.stripe.param.PaymentIntentCreateParams;
import com.stripe.param.RefundCreateParams;
import com.stripe.param.billingportal.ConfigurationCreateParams;
import com.stripe.param.billingportal.SessionCreateParams;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class StripePaymentUtils {

    /**
     * 离线扣款工具方法
     * @param customerId  cus_...
     * @param paymentMethodId pm_...
     * @param amountCents 金额（最小单位）
     * @param orderNo     业务订单号
     * @return PaymentIntent
     * @throws StripeException 任何 Stripe API 异常
     */
    public PaymentIntent payOffSession(String customerId,
                                       String paymentMethodId,
                                       Long amountCents,
                                       Long orderNo) throws StripeException {

        PaymentIntentCreateParams params = PaymentIntentCreateParams.builder()
                .setCustomer(customerId)
                .setPaymentMethod(paymentMethodId)
                .setAmount(amountCents)
                .setCurrency("USD")
                .setOffSession(true)
                .setConfirm(true)
                .putMetadata("order_id", String.valueOf(orderNo))
                .build();

        try {
            PaymentIntent pi = PaymentIntent.create(params);
            log.info("PaymentIntent {} status={}", pi.getId(), pi.getStatus());
            return pi;
        } catch (CardException e) {
            if ("authentication_required".equals(e.getCode())) {
                String clientSecret = e.getStripeError()
                        .getPaymentIntent()
                        .getClientSecret();
                log.warn("3-D Secure required, clientSecret={}", clientSecret);
                // 可抛自定义异常让控制层去通知前端
            }
            throw e;
        }
    }

    /**
     * 离线退款工具
     * @param paymentMethodId
     * @param amountCents
     * @param orderNo
     * @return
     */
    public Refund refundByPaymentIntent(String paymentMethodId, Long amountCents,Long orderNo){
        RefundCreateParams params = RefundCreateParams.builder()
                .setPaymentIntent(paymentMethodId)   // 指定要退款的 PaymentIntent
                .setAmount(amountCents)              // 指定金额；单位=最小货币单位，比如美分
                .setReason(RefundCreateParams.Reason.REQUESTED_BY_CUSTOMER)
                .putMetadata("order_id", String.valueOf(orderNo))
                .build();

        Refund refund = null;
        try {
            refund = Refund.create(params);
            log.info("Refund {} status={}", refund.getId(), refund.getStatus());
            return refund;
        } catch (StripeException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取发票
     * @param paymentIntentId
     * @return
     */
    public String getCharge(String paymentIntentId){
        PaymentIntent paymentIntent = null;
        try {
            paymentIntent = PaymentIntent.retrieve(paymentIntentId);
            String chargeId = paymentIntent.getLatestCharge();
            Charge charge   = Charge.retrieve(chargeId);
            String receiptUrl = charge.getReceiptUrl();
            return receiptUrl;
        } catch (StripeException e) {
            throw new RuntimeException(e);
        }
    }


    @SneakyThrows
    public Session getCardSession(String customerId){
        ConfigurationCreateParams.BusinessProfile business =
                ConfigurationCreateParams.BusinessProfile.builder()
                        .build();

        /* ② 必填：features —— 这里至少启用“查看发票历史”和“更新支付方式” */
        ConfigurationCreateParams.Features features =
                ConfigurationCreateParams.Features.builder()

                        // 让客户可以下载历史发票
                        .setInvoiceHistory(
                                ConfigurationCreateParams.Features.InvoiceHistory.builder()
                                        .setEnabled(true)
                                        .build())

                        // 允许客户添加 / 删除 / 设为默认卡
                        .setPaymentMethodUpdate(
                                ConfigurationCreateParams.Features.PaymentMethodUpdate.builder()
                                        .setEnabled(true)
                                        .build()).build();

        /* ③ 组装参数并创建配置 */
        ConfigurationCreateParams params =
                ConfigurationCreateParams.builder()
                        .setBusinessProfile(business)
                        .setFeatures(features)
                        .setDefaultReturnUrl("https://example.com/account")
                        .build();

        Configuration cfg = Configuration.create(params);     // 调 Stripe API
        System.out.println("Portal configuration id = " + cfg.getId());

        com.stripe.param.billingportal.SessionCreateParams params1 =
                SessionCreateParams.builder()
                        .setCustomer(customerId)
                        .setReturnUrl("https://console.myhispread.com/tos")
                        .setConfiguration(cfg.getId())
                        // 可选：锁定使用指定 configuration
                        .build();

        Session session = Session.create(params1);
        System.out.println(session.toJson());
        return session;
    }


    @SneakyThrows
    public Boolean freeze(String customerId, String paymentMethodId){
        var params = PaymentIntentCreateParams.builder()
                .setCustomer(customerId)
                .setPaymentMethod(paymentMethodId)
                .setAmount(500L)
                .setCurrency("usd")
                .setCaptureMethod(PaymentIntentCreateParams.CaptureMethod.MANUAL)
                .setConfirm(true)
                .setOffSession(true)
                .setDescription("Card verification hold $5 – auto release in 7 days")
                .putMetadata("verification_hold", "true")
                .setAutomaticPaymentMethods(PaymentIntentCreateParams.AutomaticPaymentMethods.builder().setEnabled(true).setAllowRedirects(PaymentIntentCreateParams.AutomaticPaymentMethods.AllowRedirects.NEVER).build())
                .build();
        PaymentIntent paymentIntent = PaymentIntent.create(params);
        if ("requires_capture".equals(paymentIntent.getStatus())){
            return true;
        }
        return false;
    }




}
