package com.myhispreadnlp.platform.jpa.user.info.repository;

import com.myhispreadnlp.platform.jpa.user.info.bean.UserInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserInfoRepository extends JpaRepository<UserInfo,Long> {

    public Optional<UserInfo> findUserInfoByEmail(String userEmail);

    Optional<UserInfo> findByAuth0Sub(String sub);
}
