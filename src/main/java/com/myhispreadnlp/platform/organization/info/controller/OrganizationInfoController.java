package com.myhispreadnlp.platform.organization.info.controller;

import com.myhispreadnlp.platform.common.response.organization.OrganizationInfoResponse;
import com.myhispreadnlp.platform.common.result.Result;
import com.myhispreadnlp.platform.jpa.organization.info.bean.OrganizationInfo;
import com.myhispreadnlp.platform.organization.info.service.OrganizationInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@CrossOrigin(origins = "*")
@RestController
@RequestMapping(value = "/api/private/organization/info")
@RequiredArgsConstructor
public class OrganizationInfoController {

    private final OrganizationInfoService organizationInfoService;


    @RequestMapping(value = "/selectOrganizationInfoAll", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<OrganizationInfoResponse>> selectOrganizationInfoAll() {
        return organizationInfoService.selectOrganizationInfoAll();
    }

    @RequestMapping(value = "/persistenceOrganizationInfo", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<OrganizationInfo> persistenceOrganizationInfo(@RequestParam(value = "organizationName")String organizationName) {
        return organizationInfoService.persistenceOrganizationInfo(organizationName);
    }


    @RequestMapping(value = "/updateOrganizationInfo", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> updateOrganizationInfo(@RequestParam("organizationId") Long organizationId, @RequestParam("organizationName") String organizationName) {
        return organizationInfoService.updateOrganizationInfo(organizationId, organizationName);
    }
}
