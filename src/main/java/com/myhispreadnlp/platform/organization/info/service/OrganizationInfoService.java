package com.myhispreadnlp.platform.organization.info.service;


import com.myhispreadnlp.platform.common.response.organization.OrganizationInfoResponse;
import com.myhispreadnlp.platform.common.result.Result;
import com.myhispreadnlp.platform.jpa.organization.info.bean.OrganizationInfo;


import java.util.List;

public interface OrganizationInfoService {

    public Result<List<OrganizationInfoResponse>> selectOrganizationInfoAll();

    public Result<OrganizationInfo> persistenceOrganizationInfo(String organizationName);

    public Result<String> updateOrganizationInfo(Long organizationId, String organizationName);

}
