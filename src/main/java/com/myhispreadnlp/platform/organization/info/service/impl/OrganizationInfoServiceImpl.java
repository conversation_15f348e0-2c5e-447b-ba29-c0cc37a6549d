package com.myhispreadnlp.platform.organization.info.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.myhispreadnlp.platform.common.constant.ErrorCodeEnum;
import com.myhispreadnlp.platform.common.constant.SystemConstant;
import com.myhispreadnlp.platform.common.response.organization.OrganizationInfoResponse;
import com.myhispreadnlp.platform.common.result.Result;
import com.myhispreadnlp.platform.common.snowflake.SnowflakeIdGenerator;
import com.myhispreadnlp.platform.common.utils.BillingUtils;
import com.myhispreadnlp.platform.common.utils.CurrentUser;
import com.myhispreadnlp.platform.common.utils.SystemClock;
import com.myhispreadnlp.platform.jpa.organization.info.bean.OrganizationInfo;
import com.myhispreadnlp.platform.jpa.organization.info.repository.OrganizationInfoRepository;
import com.myhispreadnlp.platform.jpa.organization.wallet.bean.OrganizationWallet;
import com.myhispreadnlp.platform.jpa.organization.wallet.bean.OrganizationWalletRecord;
import com.myhispreadnlp.platform.jpa.organization.wallet.repository.OrganizationWalletRecordRepository;
import com.myhispreadnlp.platform.jpa.organization.wallet.repository.OrganizationWalletRepository;
import com.myhispreadnlp.platform.jpa.user.bind.bean.UserOrganizationBind;
import com.myhispreadnlp.platform.jpa.user.bind.repository.UserOrganizationBindRepository;
import com.myhispreadnlp.platform.organization.info.service.OrganizationInfoService;
import jakarta.persistence.Id;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrganizationInfoServiceImpl implements OrganizationInfoService {

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final OrganizationInfoRepository organizationInfoRepository;

    private final UserOrganizationBindRepository userOrganizationBindRepository;

    private final OrganizationWalletRepository organizationWalletRepository;

    private final OrganizationWalletRecordRepository organizationWalletRecordRepository;

    private final RedisTemplate<String, String> redisTemplate;

    private final ObjectMapper objectMapper;

    private final CurrentUser currentUser;

    @Override
    public Result<List<OrganizationInfoResponse>> selectOrganizationInfoAll() {

        List<UserOrganizationBind> binds = userOrganizationBindRepository
                .findUserOrganizationBindsByUserIdAndStatuses(currentUser.get().getId(), SystemConstant.AVAILABLE);

        if (binds.isEmpty()) {
            return new Result<List<OrganizationInfoResponse>>().success(Collections.emptyList());
        }

        List<OrganizationInfoResponse> organizationInfoResponseList = new ArrayList<>();
        Iterator<UserOrganizationBind> userOrganizationBindIterator = binds.iterator();
        while (userOrganizationBindIterator.hasNext()) {
            UserOrganizationBind userOrganizationBind = userOrganizationBindIterator.next();
            OrganizationInfo organizationInfo = organizationInfoRepository.findOrganizationInfoByIdAndStatuses(userOrganizationBind.getOrganizationId(), SystemConstant.AVAILABLE);
            OrganizationInfoResponse organizationInfoResponse = new OrganizationInfoResponse(organizationInfo.getId(), organizationInfo.getOrganizationName());
            organizationInfoResponseList.add(organizationInfoResponse);
        }
        return new Result<List<OrganizationInfoResponse>>().success(organizationInfoResponseList);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<OrganizationInfo> persistenceOrganizationInfo(String organizationName) {
        List<UserOrganizationBind> binds = userOrganizationBindRepository
                .findUserOrganizationBindsByUserIdAndStatuses(currentUser.get().getId(), SystemConstant.AVAILABLE);

        if (!binds.isEmpty()) {
            return new Result<OrganizationInfo>().failed(ErrorCodeEnum.SYSTEM_ERROR);
        }

        OrganizationInfo organizationInfo = new OrganizationInfo(snowflakeIdGenerator.nextId(), 0, organizationName, SystemClock.now(), SystemConstant.AVAILABLE);
        OrganizationWallet organizationWallet = new OrganizationWallet(snowflakeIdGenerator.nextId(), organizationInfo.getId(), BillingUtils.usdToNanoCent(BigDecimal.valueOf(5)), 0L, 0L, 0L, organizationInfo.getCreateTime(), SystemConstant.AVAILABLE);
        OrganizationWalletRecord organizationWalletRecord = new OrganizationWalletRecord(snowflakeIdGenerator.nextId(), organizationInfo.getId(), organizationWallet.getOrganizationId(), BillingUtils.usdToNanoCent(BigDecimal.valueOf(5)), 0, organizationInfo.getCreateTime(), SystemConstant.AVAILABLE);
        UserOrganizationBind userOrganizationBind = new UserOrganizationBind(snowflakeIdGenerator.nextId(), currentUser.get().getId(), organizationInfo.getId(), organizationInfo.getCreateTime(), SystemConstant.AVAILABLE, SystemConstant.AVAILABLE);
        organizationInfoRepository.save(organizationInfo);
        organizationWalletRepository.save(organizationWallet);
        organizationWalletRecordRepository.save(organizationWalletRecord);
        userOrganizationBindRepository.save(userOrganizationBind);
        try {
            redisTemplate.opsForValue().set(SystemConstant.ORGANIZATION_REDIS_ID + organizationInfo.getId(), objectMapper.writeValueAsString(Map.of("code", 200, "level", organizationInfo.getLevel())));
            redisTemplate.opsForValue().set(SystemConstant.ORGANIZATION_REDIS_BALANCE + organizationInfo.getId(), String.valueOf(BillingUtils.usdToNanoCent(BigDecimal.valueOf(5L))));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return new Result<OrganizationInfo>().success(organizationInfo);
    }

    @Override
    @Transactional
    public Result<String> updateOrganizationInfo(Long organizationId, String organizationName) {
        OrganizationInfo organizationInfo = organizationInfoRepository.findOrganizationInfoById(organizationId);
        organizationInfo.setOrganizationName(organizationName);
        organizationInfoRepository.save(organizationInfo);
        return new Result<String>().success(ErrorCodeEnum.SUCCESS);

    }

    @Override
    public Result<OrganizationInfoResponse> currentOrganization() {
        var userId = currentUser.get().getId();
        var organizationId = getOrganizationId(userId);
        var organizationInfo = organizationInfoRepository.findOrganizationInfoById(organizationId);
        return Result.successResult(OrganizationInfoResponse.builder().organizationId(organizationInfo.getId()).organizationName(organizationInfo.getOrganizationName()).build());
    }

    @Override
    public Result<OrganizationInfoResponse> changeCurrentOrganization(Long organizationId) {
        var userId = currentUser.get().getId();

        List<UserOrganizationBind> binds = userOrganizationBindRepository
                .findUserOrganizationBindsByUserIdAndStatuses(userId, SystemConstant.AVAILABLE);
        if (binds.stream().map(UserOrganizationBind::getOrganizationId).noneMatch(organizationId::equals)) {
            throw new RuntimeException("no permission");
        }
        redisTemplate.opsForValue().set(SystemConstant.USER_SELECTED_ORGANIZATION + userId, String.valueOf(organizationId));
        OrganizationInfo organizationInfo = organizationInfoRepository.findOrganizationInfoById(organizationId);
        return Result.successResult(OrganizationInfoResponse.builder().organizationId(organizationInfo.getId()).organizationName(organizationInfo.getOrganizationName()).build());
    }

    private Long getOrganizationId(Long userId) {
        String cachedOrgId = redisTemplate.opsForValue().get(SystemConstant.USER_SELECTED_ORGANIZATION + userId);

        if (cachedOrgId != null) {
            return Long.valueOf(cachedOrgId);
        }

        List<UserOrganizationBind> binds = userOrganizationBindRepository
                .findUserOrganizationBindsByUserIdAndStatuses(userId, SystemConstant.AVAILABLE);

        if (binds.isEmpty()) {
            throw new RuntimeException("No organization found for user");
        }

        UserOrganizationBind userOrganizationBind = binds.getFirst();
        Long organizationId = userOrganizationBind.getOrganizationId();

        redisTemplate.opsForValue().set(
                SystemConstant.USER_SELECTED_ORGANIZATION + userId,
                String.valueOf(organizationId)
        );

        return organizationId;
    }


}
