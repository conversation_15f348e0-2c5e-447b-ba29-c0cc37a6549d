package com.myhispreadnlp.platform.organization.model.controller;

import com.myhispreadnlp.platform.common.response.organization.OrganizationModelResponse;
import com.myhispreadnlp.platform.common.result.Result;
import com.myhispreadnlp.platform.organization.model.service.OrganizationModelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/api/private/organizationModel")
@RequiredArgsConstructor
public class OrganizationModelController {

    private final OrganizationModelService organizationModelService;

    @RequestMapping(value = "/selectOrganizationModelByOrganizationId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<OrganizationModelResponse>> selectOrganizationModelByOrganizationId(@RequestParam("organizationId") Long organizationId) {
        return organizationModelService.selectOrganizationModelByOrganizationId(organizationId);
    }

    @RequestMapping(value = "/applyForDiscountByVendorModelId", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<Map<String,String>> applyForDiscountByVendorModelId(@RequestParam("organizationId") Long organizationId, @RequestParam("vendorModelId") Long vendorModelId) {
        return organizationModelService.applyForDiscountByVendorModelId(organizationId,vendorModelId);
    }


    
}
