package com.myhispreadnlp.platform.organization.model.service;


import com.myhispreadnlp.platform.common.request.PageResponse;
import com.myhispreadnlp.platform.common.response.organization.OrganizationModelResponse;
import com.myhispreadnlp.platform.common.result.Result;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Map;

public interface OrganizationModelService {

    public Result<List<OrganizationModelResponse>> selectOrganizationModelByOrganizationId(Long organizationId);

    public Result<Map<String,String>> applyForDiscountByVendorModelId(Long organizationId, Long vendorModelId);

    Result<PageResponse<OrganizationModelResponse>> pageOrganizationModelByOrganizationId(Long organizationId, String modelName, PageRequest pageRequest);
    Result<PageResponse<OrganizationModelResponse>> pageOrganizationModelByOrganizationId(Long organizationId, PageRequest pageRequest);
}
