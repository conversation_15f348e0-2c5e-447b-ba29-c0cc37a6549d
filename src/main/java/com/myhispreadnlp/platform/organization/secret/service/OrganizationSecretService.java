package com.myhispreadnlp.platform.organization.secret.service;

import com.myhispreadnlp.platform.common.request.PageResponse;
import com.myhispreadnlp.platform.common.result.Result;
import com.myhispreadnlp.platform.jpa.organization.secret.bean.OrganizationSecret;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface OrganizationSecretService {

    public Result<List<OrganizationSecret>> selectOrganizationSecretByOrganizationId(Long organizationId);

    public Result<OrganizationSecret> persistenceOrganizationSecretByOrganizationId(Long organizationId);

    public Result<OrganizationSecret> deleteOrganizationSecretByOrganizationIdAndOrganizationSecretId(Long charactersId, Long charactersSecretId);


    Result<PageResponse<OrganizationSecret>> pageOrganizationSecretByOrganizationId(Long organizationId, PageRequest of);
}
