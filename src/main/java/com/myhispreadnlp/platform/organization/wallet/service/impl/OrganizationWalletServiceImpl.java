package com.myhispreadnlp.platform.organization.wallet.service.impl;

import com.myhispreadnlp.platform.common.constant.ErrorCodeEnum;
import com.myhispreadnlp.platform.common.constant.SystemConstant;
import com.myhispreadnlp.platform.common.result.Result;
import com.myhispreadnlp.platform.common.utils.BillingUtils;
import com.myhispreadnlp.platform.common.utils.CurrentUser;
import com.myhispreadnlp.platform.jpa.organization.wallet.bean.OrganizationWallet;
import com.myhispreadnlp.platform.jpa.organization.wallet.repository.OrganizationWalletRecordRepository;
import com.myhispreadnlp.platform.jpa.organization.wallet.repository.OrganizationWalletRepository;
import com.myhispreadnlp.platform.organization.wallet.service.OrganizationWalletService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrganizationWalletServiceImpl implements OrganizationWalletService {

    private final OrganizationWalletRepository organizationWalletRepository;

    private final RedisTemplate<String, String> redisTemplate;

    private final CurrentUser currentUser;


    @Override
    @Transactional
    public Result<String> updateAutoWallet(Long organizationId, Long minimumAmount, Long rechargeAmount) {
        if (1 == currentUser.getPermission()){
            OrganizationWallet organizationWallet = organizationWalletRepository.findOrganizationWalletByOrganizationId(organizationId);
            organizationWallet.setMinimumAmount(minimumAmount * SystemConstant.NANOS_PER_DOLLAR);
            organizationWallet.setRechargeAmount(rechargeAmount * SystemConstant.NANOS_PER_DOLLAR);
            organizationWalletRepository.save(organizationWallet);
            return new Result<String>().success(ErrorCodeEnum.SUCCESS);
        }
        return new Result<String>().success(ErrorCodeEnum.WALLET_ERROR);
    }


    @Override
    public Result<Map<String, Object>> getWalletBalance(Long organizationId) {
        OrganizationWallet organizationWallet = organizationWalletRepository.findOrganizationWalletByOrganizationId(organizationId);

        Map<String,Object> result = new HashMap<String,Object>();
        result.put("balance", BillingUtils.nanosToUSD(Long.valueOf(redisTemplate.opsForValue().get(SystemConstant.ORGANIZATION_REDIS_BALANCE+organizationId))));
        result.put("minimumAmount",BillingUtils.nanosToUSD(organizationWallet.getMinimumAmount()));
        result.put("rechargeAmount",BillingUtils.nanosToUSD(organizationWallet.getRechargeAmount()));
        return new Result<Map<String,Object>>().success(result);
    }
}
