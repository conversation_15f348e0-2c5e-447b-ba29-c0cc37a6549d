package com.myhispreadnlp.platform.user.bind.controller;

import com.myhispreadnlp.platform.common.result.Result;
import com.myhispreadnlp.platform.user.bind.service.UserOrganizationBindService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

@Slf4j
@CrossOrigin(origins = "*")
@RestController
@RequestMapping(value = "/api/private/organization/team")
@RequiredArgsConstructor
public class UserOrganizationBindController {

    private final UserOrganizationBindService userOrganizationBindService;

    @RequestMapping(value = "/persistenceUserOrganizationBind", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> persistenceUserOrganizationBind(@RequestParam("organizationId") Long organizationId,
                                                          @RequestParam("permission") Integer permission,
                                                          @RequestParam("userEmail") String userEmail) {

        return userOrganizationBindService.persistenceUserOrganizationBind(organizationId, permission, userEmail);
    }


    @RequestMapping(value = "/selectUserOrganizationBindByOrganizationId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<HashMap<String, String>>> selectUserOrganizationBindByOrganizationId(
            @RequestParam("organizationId") Long organizationId) {

        return userOrganizationBindService.selectUserOrganizationBindByOrganizationId(organizationId);
    }

    @RequestMapping(value = "/updateUserOrganizationBindByOrganizationId", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String>  updateUserOrganizationBindByOrganizationId(@RequestParam("organizationId") Long organizationId,
                                                                      @RequestParam("permission") Integer permission,
                                                                      @RequestParam("userEmail") String userEmail) {

        return userOrganizationBindService.updateUserOrganizationBindByOrganizationId(organizationId, permission, userEmail);
    }

    @RequestMapping(value = "/deleteUserOrganizationBindByOrganizationId", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String>  deleteUserOrganizationBindByOrganizationId(@RequestParam("organizationId") Long organizationId,
                                                                      @RequestParam("userEmail") String userEmail) {
        return userOrganizationBindService.deleteUserOrganizationBindByOrganizationId(organizationId, userEmail);
    }


}
