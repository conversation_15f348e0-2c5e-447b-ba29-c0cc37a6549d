package com.myhispreadnlp.platform.user.bind.service;

import com.myhispreadnlp.platform.common.result.Result;

import java.util.HashMap;
import java.util.List;

public interface UserOrganizationBindService {

    public Result<String> persistenceUserOrganizationBind(Long organizationId, Integer permission, String userEmail);

    public Result<List<HashMap<String, String>>> selectUserOrganizationBindByOrganizationId(Long organizationId);

    public Result<String> updateUserOrganizationBindByOrganizationId(Long organizationId, Integer permission, String userEmail);

    public Result<String>  deleteUserOrganizationBindByOrganizationId(Long organizationId, String userEmail);
}
