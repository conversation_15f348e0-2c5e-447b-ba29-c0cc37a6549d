package com.myhispreadnlp.platform.user.bind.service.impl;

import com.myhispreadnlp.platform.common.constant.ErrorCodeEnum;
import com.myhispreadnlp.platform.common.constant.SystemConstant;
import com.myhispreadnlp.platform.common.result.Result;
import com.myhispreadnlp.platform.common.snowflake.SnowflakeIdGenerator;
import com.myhispreadnlp.platform.common.utils.CurrentUser;
import com.myhispreadnlp.platform.common.utils.SystemClock;
import com.myhispreadnlp.platform.jpa.user.bind.bean.UserOrganizationBind;
import com.myhispreadnlp.platform.jpa.user.bind.repository.UserOrganizationBindRepository;
import com.myhispreadnlp.platform.jpa.user.info.bean.UserInfo;
import com.myhispreadnlp.platform.jpa.user.info.repository.UserInfoRepository;
import com.myhispreadnlp.platform.user.bind.service.UserOrganizationBindService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserOrganizationBindServiceImpl implements UserOrganizationBindService {

    private final UserInfoRepository userInfoRepository;

    private final UserOrganizationBindRepository userOrganizationBindRepository;

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final CurrentUser currentUser;


    @Override
    public Result<String>  persistenceUserOrganizationBind(Long organizationId, Integer permission, String userEmail) {
        //权限通过
        if (currentUser.getPermission() == 1) {
            var userInfo = userInfoRepository.findUserInfoByEmail(userEmail)
                    .orElseThrow(() -> new CognitionException(ErrorCodeEnum.USER_NOT_FOUND));
            if (userInfo == null) {
                return new Result<String>().failed(ErrorCodeEnum.SYSTEM_ERROR);
            }
            UserOrganizationBind userOrganizationBind = userOrganizationBindRepository.findUserOrganizationBindByOrganizationIdAndUserId(organizationId, userInfo.getId());
            if (userOrganizationBind == null) {
                userOrganizationBind = new UserOrganizationBind(snowflakeIdGenerator.nextId(), userInfo.getId(), organizationId, SystemClock.now(), permission, SystemConstant.AVAILABLE);
            }
            userOrganizationBind.setStatuses(SystemConstant.AVAILABLE);
            userOrganizationBind.setUserId(userInfo.getId());
            userOrganizationBind.setPermission(permission);
            userOrganizationBind.setCreateTime(SystemClock.now());
            userOrganizationBindRepository.save(userOrganizationBind);
            return new Result<String>().failed(ErrorCodeEnum.SUCCESS);
        }

        return new Result<String>().failed(ErrorCodeEnum.SYSTEM_ERROR);
    }
    @Override
    public Result<List<HashMap<String, String>>> selectUserOrganizationBindByOrganizationId(Long organizationId) {
        List<HashMap<String, String>> userInfos = new ArrayList<HashMap<String, String>>();
        List<UserOrganizationBind> userOrganizationBindList = userOrganizationBindRepository.findUserOrganizationBindsByOrganizationIdAndStatuses(organizationId, SystemConstant.AVAILABLE);
        ListIterator<UserOrganizationBind> userOrganizationBindListIterator = userOrganizationBindList.listIterator();
        while (userOrganizationBindListIterator.hasNext()) {
            UserOrganizationBind userOrganizationBind = userOrganizationBindListIterator.next();
            Optional<UserInfo> userInfo = userInfoRepository.findById(userOrganizationBind.getUserId());
            HashMap<String, String> hashMap = new HashMap<String, String>();
            hashMap.put("email", userInfo.get().getEmail());
            hashMap.put("name", userInfo.get().getNickname());
            hashMap.put("permission", userOrganizationBind.getPermission().toString());
            userInfos.add(hashMap);
        }
        return new Result<List<HashMap<String,String>>>().success(userInfos);
    }

    @Override
    public Result<String> updateUserOrganizationBindByOrganizationId(Long organizationId, Integer permission, String userEmail) {
        //权限通过
        if (currentUser.getPermission() == 1) {
            var userInfo = userInfoRepository.findUserInfoByEmail(userEmail)
                    .orElseThrow(() -> new RuntimeException("User not found"));
            UserOrganizationBind userOrganizationBind = userOrganizationBindRepository.findUserOrganizationBindByOrganizationIdAndUserId(organizationId, userInfo.getId());
            userOrganizationBind.setUserId(userInfo.getId());
            userOrganizationBind.setPermission(permission);
            userOrganizationBind.setCreateTime(SystemClock.now());
            userOrganizationBind.setStatuses(SystemConstant.AVAILABLE);
            userOrganizationBindRepository.save(userOrganizationBind);
            return new Result<String>().failed(ErrorCodeEnum.SUCCESS);
        }
        return new Result<String>().failed(ErrorCodeEnum.SYSTEM_ERROR);
    }

    @Override
    public Result<String>  deleteUserOrganizationBindByOrganizationId(Long organizationId, String userEmail) {
        //权限通过
        if (currentUser.getPermission() == 1) {
            var userInfo = userInfoRepository.findUserInfoByEmail(userEmail)
                    .orElseThrow(() -> new RuntimeException("User not found"));
            UserOrganizationBind userOrganizationBind = userOrganizationBindRepository.findUserOrganizationBindByOrganizationIdAndUserId(organizationId, userInfo.getId());
            userOrganizationBind.setStatuses(SystemConstant.UNAVAILABLE);
            userOrganizationBindRepository.save(userOrganizationBind);
            return new Result<String>().failed(ErrorCodeEnum.SUCCESS);
        }
        return new Result<String>().failed(ErrorCodeEnum.SYSTEM_ERROR);

    }
}
