package com.myhispreadnlp.platform.user.info.service.impl;

import com.myhispreadnlp.platform.common.snowflake.SnowflakeIdGenerator;
import com.myhispreadnlp.platform.common.utils.SystemClock;
import com.myhispreadnlp.platform.jpa.user.info.bean.UserInfo;
import com.myhispreadnlp.platform.jpa.user.info.repository.UserInfoRepository;
import com.myhispreadnlp.platform.user.info.service.UserInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserInfoServiceImpl implements UserInfoService {

    private final UserInfoRepository userInfoRepository;

    private final SnowflakeIdGenerator snowflakeIdGenerator;


    @Transactional
    public UserInfo ensureUser(Map<String, Object> claims) {

        String sub = (String) claims.get("sub");

        /* 1. 先按 auth0_sub 查询 */
        Optional<UserInfo> existing = userInfoRepository.findByAuth0Sub(sub);
        if (existing.isPresent()) return existing.get();

        /* 2. 不存在则插入 —— 可能并发冲突 */
        UserInfo u = new UserInfo();
        u.setId(snowflakeIdGenerator.nextId());
        u.setAuth0Sub(sub);
        u.setEmail((String) claims.get("email"));
        u.setName((String) claims.getOrDefault("name", "未命名"));
        u.setPicture((String) claims.get("picture"));
        u.setCreateTime(SystemClock.now());

        try {
            return userInfoRepository.saveAndFlush(u);            // 立刻执行 INSERT
        } catch (DataIntegrityViolationException dup) {
            // 有别的线程抢先插入 -> 再查一次返回
            return userInfoRepository.findByAuth0Sub(sub).orElseThrow();
        }
    }

}
